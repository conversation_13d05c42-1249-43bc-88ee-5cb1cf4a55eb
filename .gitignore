project/actor/actor/electron/main/env-variables.json
.eslintcache
*.jsonl.gz
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class
pip-wheel-metadata/

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
*.npz
# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Virtualenvs
.ve

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb
.ipynb_checkpoints

# IDEA
.idea/

# Qt Creator
*.cflags
*.cxxflags
*.creator.user

# VIM
*.swp

# Emacs
*~

# IDEs
.vscode

.sublime-project
.sublime-workspace

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

*.tfrecords
*.npy

# SageMath parsed files
*.sage.py

# Environments
.env
.retrieval_env
.env.leave
.envrc
.venv
venv/
env.bak/
venv.bak/
*.rdb

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json

# javascript
node_modules/
.svelte-kit
.vite
.next/

# general
local/
*.mp4
*.ogv
.DS_Store

# rl team
.build
.assets
docker_build_staging

# VS Code workspace configuration
*.code-workspace

# Redis
dump.rdb
multi-agent/minecraft/wandb
multi-agent/ci/buildkite/agent_management/buildkite-agent-parametrized.yaml
multi-agent/ci/buildkite/agent_management/buildkite-agent-parametrized-lowpri.yaml

fivesight/fivesight/yoda/basic_data_structures/fast_int_permutation.cpp

# ignoring this since it's copied on the Mac from a fake dir
din/din/nccl.py

# large image file
alignment/neuron_explainer_app/static/logo.png

# world of bits file (rl/wob2)
tmp_wob_files

# Kubernetes certificate during repo setup
certificate.json

# Sapling (https://sapling-scm.com/) is a git-protocol-compatible VCS
# with a a nice stacked diff workflow.  Many tools expect git, so it's
# convenient to work in a hybrid checkout with a single working
# directory shared by both git and sapling.  These two entries ensure
# that sapling is hidden from git, and vice-versa.
.sl/
.git/
# In some configurations .git-under-sapling is a file rather than directory
.git

# For local development, sa-server will store a fake cosmos store
# on disk at this location
fake_cosmos_store

# ctags
TAGS
tags
.sugarjar.local.yaml
.sugarjar.local-overwrite.yaml

# common throw-away files in the root
/0
/1
/2
/3
/4
/5
/6
/7
/8
/9

# Multi SWE Bench analysis outputs
/project/evals_msft/benchmarks/multi_swe_bench/analysis/outputs

# Multi SWE Bench batch validation logs
/project/evals_msft/benchmarks/multi_swe_bench/msweb/batch_validation_logs

# Multi SWE Bench results
/project/evals_msft/benchmarks/multi_swe_bench/instances_ranking/codex-mini_oai_results

# Multi SWE Bench histogram plotting virtual environment
/project/evals_msft/benchmarks/multi_swe_bench/instances_ranking/histogram_env

# Multi SWE Bench data conversion to peaval format
/project/deep_swe_eval_msft/deep_swe_eval_msft/msweb/scripts/consolidated
/project/deep_swe_eval_msft/deep_swe_eval_msft/msweb/scripts/peaval_formatted*

# Outputs of SBV debug jobs
/project/deep_swe_eval_msft/deep_swe_eval_msft/swe_bench/scripts/outputs*
/project/deep_swe_eval_msft/deep_swe_eval_msft/swe_bench/scripts/gpt-log*

# Notebook for data exploration, data pre-processing inputs and outputs
/project/berry_repro_msft/berry_repro_msft/data/codeforces/sandbox.ipynb
/project/berry_repro_msft/berry_repro_msft/data/codeforces/inputs
/project/berry_repro_msft/berry_repro_msft/data/codeforces/outputs

# Web-Bench source code
/project/evals_msft/benchmarks/web_bench/web-bench