diff --git a/.gitignore b/.gitignore
index 560a5c0..8fb768f 100644
--- a/.gitignore
+++ b/.gitignore
@@ -129,3 +129,6 @@ dist-storybook/
 # Heft temporary files
 .cache/
 .heft/
+
+# somehow I ended up with this folder when installing from source...
+yes
diff --git a/apps/eval/src/model.json b/apps/eval/src/model.json
index 1d7ca1a..cdb5a78 100644
--- a/apps/eval/src/model.json
+++ b/apps/eval/src/model.json
@@ -279,6 +279,27 @@
       "title": "ollama/mistral-small",
       "provider": "ollama",
       "model": "mistral-small"
+    },
+    {
+      "title": "azure-openai",
+      "provider": "azure-openai",
+      "model": "{{AOAI_MODEL}}",
+      "deployment": "{{AOAI_DEPLOYMENT}}",
+      "apiBase": "{{AOAI_ENDPOINT}}",
+      "apiKey": "{{AOAI_API_KEY}}",
+      "apiVersion": "{{AOAI_API_VERSION}}"
+    },
+    {
+      "title": "orange-tap",
+      "provider": "orange-tap",
+      "model": "{{ORANGE_TAP_MODEL}}",
+      "apiBase": "{{ORANGE_TAP_ENDPOINT}}",
+      "resourceId": "{{ORANGE_TAP_RESOURCE_ID}}",
+      "tokenRefreshIntervalMinutes": "{{ORANGE_TAP_TOKEN_REFRESH_INTERVAL_MINUTES}}",
+      "serviceUrl": "{{ORANGE_TAP_SERVICE_URL}}",
+      "xBusUser": "{{ORANGE_TAP_X_BUS_USER}}",
+      "xBusSnapshot": "{{ORANGE_TAP_X_BUS_SNAPSHOT}}",
+      "xBusRenderer": "{{ORANGE_TAP_X_BUS_RENDERER}}"
     }
   ]
 }
diff --git a/package-lock.json b/package-lock.json
new file mode 100644
index 0000000..5414033
--- /dev/null
+++ b/package-lock.json
@@ -0,0 +1,54 @@
+{
+  "name": "Web-Bench",
+  "lockfileVersion": 3,
+  "requires": true,
+  "packages": {
+    "": {
+      "dependencies": {
+        "node-fetch": "^2.7.0"
+      }
+    },
+    "node_modules/node-fetch": {
+      "version": "2.7.0",
+      "resolved": "https://registry.npmjs.org/node-fetch/-/node-fetch-2.7.0.tgz",
+      "integrity": "sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==",
+      "license": "MIT",
+      "dependencies": {
+        "whatwg-url": "^5.0.0"
+      },
+      "engines": {
+        "node": "4.x || >=6.0.0"
+      },
+      "peerDependencies": {
+        "encoding": "^0.1.0"
+      },
+      "peerDependenciesMeta": {
+        "encoding": {
+          "optional": true
+        }
+      }
+    },
+    "node_modules/tr46": {
+      "version": "0.0.3",
+      "resolved": "https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz",
+      "integrity": "sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==",
+      "license": "MIT"
+    },
+    "node_modules/webidl-conversions": {
+      "version": "3.0.1",
+      "resolved": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz",
+      "integrity": "sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==",
+      "license": "BSD-2-Clause"
+    },
+    "node_modules/whatwg-url": {
+      "version": "5.0.0",
+      "resolved": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz",
+      "integrity": "sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==",
+      "license": "MIT",
+      "dependencies": {
+        "tr46": "~0.0.3",
+        "webidl-conversions": "^3.0.0"
+      }
+    }
+  }
+}
diff --git a/package.json b/package.json
new file mode 100644
index 0000000..f513339
--- /dev/null
+++ b/package.json
@@ -0,0 +1,5 @@
+{
+  "dependencies": {
+    "node-fetch": "^2.7.0"
+  }
+}
diff --git a/sandbox/config.json5 b/sandbox/config.json5
new file mode 100644
index 0000000..3898a3d
--- /dev/null
+++ b/sandbox/config.json5
@@ -0,0 +1,11 @@
+{
+  models: [
+    'azure-openai',
+    //'orange-tap',
+  ],
+  // Evaluate specific projects only, comment out to evaluate all projects
+  "projects": ["@web-bench/react"],
+  //"projects": ["@web-bench/react", "@web-bench/vue", "@web-bench/svelte"],
+  //"projects": ["@web-bench/survey", "@web-bench/tailwind"],
+  "agentMode": "local"
+}
\ No newline at end of file
diff --git a/sandbox/env.env b/sandbox/env.env
new file mode 100644
index 0000000..5c765ec
--- /dev/null
+++ b/sandbox/env.env
@@ -0,0 +1,26 @@
+# For evaluating models deployed to AOAI endpoints
+
+## o3-mini
+AOAI_MODEL="o3-mini"
+AOAI_DEPLOYMENT="o3-mini"
+AOAI_ENDPOINT="https://azure-genai-dr-vteam.openai.azure.com/"
+AOAI_API_KEY="your-aoai-api-key"
+AOAI_API_VERSION="2024-12-01-preview"
+
+
+# For evaluating models deployed to Orange bus and accessed via Orange Tap
+
+## Orange Tap Configuration
+ORANGE_TAP_RESOURCE_ID=2a750fd4-529b-4678-b332-6331e201131c
+ORANGE_TAP_TOKEN_REFRESH_INTERVAL_MINUTES=45
+
+## Orange Tap endpoint info
+ORANGE_TAP_SERVICE_URL=http://bus-o4-15-model-0-svc-8009.swang.svc.cluster.local:8009
+ORANGE_TAP_ENDPOINT=https://***************:443
+
+## Orange Tap model and bus info
+### o3-mini
+ORANGE_TAP_X_BUS_USER=msft
+ORANGE_TAP_X_BUS_SNAPSHOT=az://orngcresco/models/snapshots/models.tc/neutrino-o1.5-mini-base-before-posttraining-transferred-********-decrypted
+ORANGE_TAP_X_BUS_RENDERER=harmony_v4.0.15_berry_v3_1mil_orion_lpe
+ORANGE_TAP_MODEL=o3-mini
\ No newline at end of file
diff --git a/sandbox/environment.yml b/sandbox/environment.yml
new file mode 100644
index 0000000..5fa87e3
--- /dev/null
+++ b/sandbox/environment.yml
@@ -0,0 +1,10 @@
+name: web_bench_env
+channels:
+  - conda-forge
+  - defaults
+dependencies:
+  - python=3.11
+  - nodejs=22
+  - pip
+  - pip:
+    - openai
diff --git a/sandbox/examples_model.json b/sandbox/examples_model.json
new file mode 100644
index 0000000..860b3f4
--- /dev/null
+++ b/sandbox/examples_model.json
@@ -0,0 +1,21 @@
+{
+  "title": "azure-openai",
+  "provider": "azure-openai",
+  "model": "{{AOAI_MODEL}}",
+  "deployment": "{{AOAI_DEPLOYMENT}}",
+  "apiBase": "{{AOAI_ENDPOINT}}",
+  "apiKey": "{{AOAI_API_KEY}}",
+  "apiVersion": "{{AOAI_API_VERSION}}"
+},
+{
+  "title": "orange-tap",
+  "provider": "orange-tap",
+  "model": "{{ORANGE_TAP_MODEL}}",
+  "apiBase": "{{ORANGE_TAP_ENDPOINT}}",
+  "resourceId": "{{ORANGE_TAP_RESOURCE_ID}}",
+  "tokenRefreshIntervalMinutes": "{{ORANGE_TAP_TOKEN_REFRESH_INTERVAL_MINUTES}}",
+  "serviceUrl": "{{ORANGE_TAP_SERVICE_URL}}",
+  "xBusUser": "{{ORANGE_TAP_X_BUS_USER}}",
+  "xBusSnapshot": "{{ORANGE_TAP_X_BUS_SNAPSHOT}}",
+  "xBusRenderer": "{{ORANGE_TAP_X_BUS_RENDERER}}"
+}
diff --git a/sandbox/extract_evaluation_results.py b/sandbox/extract_evaluation_results.py
new file mode 100755
index 0000000..8f98fb8
--- /dev/null
+++ b/sandbox/extract_evaluation_results.py
@@ -0,0 +1,386 @@
+#!/usr/bin/env python3
+"""
+Extract and concatenate evaluation results from report markdown files.
+
+This script processes evaluation folders and extracts specific sections from
+.report.md files, combining them into a single overall_result.md file.
+"""
+
+import os
+import sys
+import argparse
+import re
+from pathlib import Path
+from typing import List, Tuple, Optional, Dict
+
+
+def extract_metrics(content: str, file_path: Path) -> Tuple[Optional[float], Optional[float], Optional[float]]:
+    """
+    Extract pass@1, pass@2, and error@1 values from markdown content.
+    
+    Returns:
+        Tuple of (pass@1, pass@2, error@1) or None values if not found
+    """
+    pass_at_1 = None
+    pass_at_2 = None
+    error_at_1 = None
+    
+    # Look for the metrics table
+    lines = content.split('\n')
+    in_metrics_section = False
+    
+    for line in lines:
+        line_stripped = line.strip()
+        
+        if line_stripped == "## Metrics":
+            in_metrics_section = True
+            continue
+        elif line_stripped.startswith("## ") and line_stripped != "## Metrics":
+            in_metrics_section = False
+            continue
+        
+        if in_metrics_section and "|" in line:
+            # This might be a table row
+            parts = [part.strip() for part in line.split("|")]
+            if len(parts) >= 3:
+                metric_name = parts[1].strip() if len(parts) > 1 else ""
+                metric_value = parts[2].strip() if len(parts) > 2 else ""
+                
+                # Try to extract percentage value
+                if metric_value.endswith('%'):
+                    try:
+                        value = float(metric_value[:-1])
+                        if metric_name == "pass@1":
+                            pass_at_1 = value
+                        elif metric_name == "pass@2":
+                            pass_at_2 = value
+                        elif metric_name == "error@1":
+                            error_at_1 = value
+                    except ValueError:
+                        continue
+    
+    return pass_at_1, pass_at_2, error_at_1
+
+
+def find_report_files(input_path: Path) -> List[Path]:
+    """Find all .report.md files in subfolders (and sub-subfolders) of the input path."""
+    report_files = []
+    
+    if not input_path.exists():
+        print(f"Error: Path '{input_path}' does not exist")
+        return []
+    
+    if not input_path.is_dir():
+        print(f"Error: Path '{input_path}' is not a directory")
+        return []
+    
+    # Use recursive glob to find all .report.md files
+    all_report_files = list(input_path.rglob("*.report.md"))
+    
+    # Group by immediate subfolder to verify one per subfolder constraint
+    subfolder_files = {}
+    
+    for report_file in all_report_files:
+        # Find the immediate subfolder relative to input_path
+        try:
+            relative_path = report_file.relative_to(input_path)
+            immediate_subfolder = relative_path.parts[0]
+            
+            if immediate_subfolder not in subfolder_files:
+                subfolder_files[immediate_subfolder] = []
+            subfolder_files[immediate_subfolder].append(report_file)
+        except ValueError:
+            # This shouldn't happen with rglob, but just in case
+            continue
+    
+    # Check each subfolder
+    for subfolder in input_path.iterdir():
+        if subfolder.is_dir():
+            subfolder_name = subfolder.name
+            
+            if subfolder_name not in subfolder_files:
+                print(f"Warning: No .report.md file found in '{subfolder}' or its subdirectories")
+            elif len(subfolder_files[subfolder_name]) == 1:
+                report_files.append(subfolder_files[subfolder_name][0])
+            else:
+                print(f"Error: Multiple .report.md files found in '{subfolder}' and its subdirectories: {[f.name for f in subfolder_files[subfolder_name]]}")
+                # Still add them all, but flag as error
+                report_files.extend(subfolder_files[subfolder_name])
+    
+    return report_files
+
+
+def extract_sections(content: str, file_path: Path) -> Tuple[str, List[str]]:
+    """
+    Extract the required sections from markdown content.
+    
+    Returns:
+        Tuple of (extracted_content, list_of_alerts)
+    """
+    alerts = []
+    
+    # Split content into lines for processing
+    lines = content.split('\n')
+    
+    # Find section boundaries
+    metrics_start = None
+    metrics_end = None
+    results_start = None
+    results_end = None
+    
+    for i, line in enumerate(lines):
+        line_stripped = line.strip()
+        
+        # Look for section headers
+        if line_stripped == "## Metrics":
+            if metrics_start is not None:
+                alerts.append(f"Multiple '## Metrics' sections found in {file_path}")
+            metrics_start = i
+        elif line_stripped == "## Evaluation Results":
+            if results_start is not None:
+                alerts.append(f"Multiple '## Evaluation Results' sections found in {file_path}")
+            results_start = i
+        elif line_stripped.startswith("## ") and line_stripped not in ["## Metrics", "## Evaluation Results"]:
+            # This is another section header, could be end of our sections
+            if metrics_start is not None and metrics_end is None:
+                metrics_end = i
+            elif results_start is not None and results_end is None:
+                results_end = i
+    
+    # Handle end of file cases
+    if metrics_start is not None and metrics_end is None:
+        metrics_end = len(lines)
+    if results_start is not None and results_end is None:
+        results_end = len(lines)
+    
+    # Check for missing sections
+    if metrics_start is None:
+        alerts.append(f"'## Metrics' section not found in {file_path}")
+    if results_start is None:
+        alerts.append(f"'## Evaluation Results' section not found in {file_path}")
+    
+    # Extract content before ## Metrics
+    before_metrics = []
+    if metrics_start is not None:
+        before_metrics = lines[:metrics_start]
+    else:
+        # If no metrics section, take everything before first ## section
+        for i, line in enumerate(lines):
+            if line.strip().startswith("## "):
+                before_metrics = lines[:i]
+                break
+        else:
+            before_metrics = lines  # No sections found, take everything
+    
+    # Extract Metrics section
+    metrics_content = []
+    if metrics_start is not None:
+        end_idx = metrics_end if metrics_end is not None else len(lines)
+        metrics_content = lines[metrics_start:end_idx]
+        
+        # Check if section is empty (only has header)
+        if len(metrics_content) <= 1 or all(line.strip() == "" for line in metrics_content[1:]):
+            alerts.append(f"'## Metrics' section is empty in {file_path}")
+    
+    # Extract Evaluation Results section
+    results_content = []
+    if results_start is not None:
+        end_idx = results_end if results_end is not None else len(lines)
+        results_content = lines[results_start:end_idx]
+        
+        # Check if section is empty (only has header)
+        if len(results_content) <= 1 or all(line.strip() == "" for line in results_content[1:]):
+            alerts.append(f"'## Evaluation Results' section is empty in {file_path}")
+    
+    # Combine all sections
+    extracted_lines = []
+    extracted_lines.extend(before_metrics)
+    if metrics_content:
+        extracted_lines.extend(metrics_content)
+    if results_content:
+        extracted_lines.extend(results_content)
+    
+    return '\n'.join(extracted_lines), alerts
+
+
+def main():
+    parser = argparse.ArgumentParser(
+        description="Extract and concatenate evaluation results from report markdown files"
+    )
+    parser.add_argument(
+        "input_path",
+        help="Path to evaluation folder (supports relative paths, '.', or '..')"
+    )
+    parser.add_argument(
+        "-o", "--output",
+        default="overall_result.md",
+        help="Output file name (default: overall_result.md)"
+    )
+    
+    args = parser.parse_args()
+    
+    # Resolve input path
+    input_path = Path(args.input_path).resolve()
+    print(f"Processing evaluation folder: {input_path}")
+    
+    # Find all report files
+    report_files = find_report_files(input_path)
+    
+    if not report_files:
+        print("No report files found. Exiting.")
+        return 1
+    
+    print(f"Found {len(report_files)} report files")
+    
+    # Process each file
+    all_alerts = []
+    extracted_contents = []
+    
+    # Dictionaries to store metrics
+    pass_at_1_dict = {}
+    pass_at_2_dict = {}
+    error_at_1_dict = {}
+    failed_extractions = []
+    
+    for report_file in sorted(report_files):
+        print(f"Processing: {report_file}")
+        
+        try:
+            with open(report_file, 'r', encoding='utf-8') as f:
+                content = f.read()
+            
+            # Get subfolder name (immediate parent folder relative to input_path)
+            relative_path = report_file.relative_to(input_path)
+            subfolder_name = relative_path.parts[0]
+            
+            # Extract metrics
+            pass_at_1, pass_at_2, error_at_1 = extract_metrics(content, report_file)
+            pass_at_1_dict[subfolder_name] = pass_at_1
+            pass_at_2_dict[subfolder_name] = pass_at_2
+            error_at_1_dict[subfolder_name] = error_at_1
+            
+            # Check if extraction failed
+            if pass_at_1 is None and pass_at_2 is None and error_at_1 is None:
+                failed_extractions.append(subfolder_name)
+            
+            extracted_content, alerts = extract_sections(content, report_file)
+            all_alerts.extend(alerts)
+            
+            # Store the extracted content with file path
+            file_section = f"File: {report_file}\n{'$' * 50}\n{extracted_content}"
+            extracted_contents.append(file_section)
+            
+        except Exception as e:
+            error_msg = f"Error reading {report_file}: {e}"
+            print(f"ERROR: {error_msg}")
+            all_alerts.append(error_msg)
+            
+            # Add to failed extractions
+            try:
+                relative_path = report_file.relative_to(input_path)
+                subfolder_name = relative_path.parts[0]
+                failed_extractions.append(subfolder_name)
+                pass_at_1_dict[subfolder_name] = None
+                pass_at_2_dict[subfolder_name] = None
+                error_at_1_dict[subfolder_name] = None
+            except:
+                pass
+    
+    # Calculate statistics
+    successful_pass_at_1 = sum(1 for v in pass_at_1_dict.values() if v is not None)
+    successful_pass_at_2 = sum(1 for v in pass_at_2_dict.values() if v is not None)
+    successful_error_at_1 = sum(1 for v in error_at_1_dict.values() if v is not None)
+    
+    # Calculate averages (excluding None values)
+    valid_pass_at_1 = [v for v in pass_at_1_dict.values() if v is not None]
+    valid_pass_at_2 = [v for v in pass_at_2_dict.values() if v is not None]
+    valid_error_at_1 = [v for v in error_at_1_dict.values() if v is not None]
+    
+    avg_pass_at_1 = sum(valid_pass_at_1) / len(valid_pass_at_1) if valid_pass_at_1 else 0
+    avg_pass_at_2 = sum(valid_pass_at_2) / len(valid_pass_at_2) if valid_pass_at_2 else 0
+    avg_error_at_1 = sum(valid_error_at_1) / len(valid_error_at_1) if valid_error_at_1 else 0
+    
+    # Create output file path (in the input directory)
+    output_path = input_path / args.output
+    
+    # Summary at the beginning
+    summary = [
+        f"# Overall Evaluation Results",
+        f"",
+        f"**Summary:**",
+        f"- Total markdown files found: {len(report_files)}",
+        f"- Files with alerts/errors: {len([f for f in report_files if any(str(f) in alert for alert in all_alerts)])}",
+        f"- Total alerts/errors: {len(all_alerts)}",
+        f"",
+        f"**Metrics Extraction:**",
+        f"- Successfully extracted pass@1: {successful_pass_at_1}/{len(report_files)} files",
+        f"- Successfully extracted pass@2: {successful_pass_at_2}/{len(report_files)} files", 
+        f"- Successfully extracted error@1: {successful_error_at_1}/{len(report_files)} files",
+        f"",
+        f"**Average Metrics:**",
+        f"- Average pass@1: {avg_pass_at_1:.1f}%",
+        f"- Average pass@2: {avg_pass_at_2:.1f}%",
+        f"- Average error@1: {avg_error_at_1:.1f}%",
+        f"",
+    ]
+    
+    if failed_extractions:
+        failed_unique = sorted(set(failed_extractions))
+        summary.extend([
+            f"**Subfolders with failed metrics extraction ({len(failed_unique)}):**",
+            ""
+        ])
+        for failed in failed_unique:
+            summary.append(f"- {failed}")
+        summary.append("")
+    
+    if all_alerts:
+        summary.extend([
+            f"**Alerts and Errors:**",
+            ""
+        ])
+        for alert in all_alerts:
+            summary.append(f"- {alert}")
+        summary.append("")
+    
+    summary.extend([
+        f"{'=' * 50}",
+        f"",
+    ])
+    
+    # Write output file
+    try:
+        with open(output_path, 'w', encoding='utf-8') as f:
+            f.write('\n'.join(summary))
+            f.write('\n'.join(extracted_contents))
+        
+        print(f"\nOutput written to: {output_path}")
+        print(f"Total files processed: {len(report_files)}")
+        print(f"Metrics extraction success rates:")
+        print(f"  - pass@1: {successful_pass_at_1}/{len(report_files)}")
+        print(f"  - pass@2: {successful_pass_at_2}/{len(report_files)}")
+        print(f"  - error@1: {successful_error_at_1}/{len(report_files)}")
+        print(f"Average metrics:")
+        print(f"  - pass@1: {avg_pass_at_1:.1f}%")
+        print(f"  - pass@2: {avg_pass_at_2:.1f}%")
+        print(f"  - error@1: {avg_error_at_1:.1f}%")
+        print(f"Total alerts/errors: {len(all_alerts)}")
+        
+        if failed_extractions:
+            failed_unique = sorted(set(failed_extractions))
+            print(f"Failed extractions: {failed_unique}")
+        
+        if all_alerts:
+            print("\nAlerts/Errors:")
+            for alert in all_alerts:
+                print(f"  - {alert}")
+        
+        return 0
+        
+    except Exception as e:
+        print(f"Error writing output file: {e}")
+        return 1
+
+
+if __name__ == "__main__":
+    sys.exit(main())
diff --git a/sandbox/test_aoai_endpoint.py b/sandbox/test_aoai_endpoint.py
new file mode 100644
index 0000000..f5f28a5
--- /dev/null
+++ b/sandbox/test_aoai_endpoint.py
@@ -0,0 +1,32 @@
+import os
+from openai import AzureOpenAI
+
+endpoint = "https://azure-genai-dr-vteam.openai.azure.com/"
+model_name = "o3-mini"
+deployment = "o3-mini"
+
+subscription_key = "insert-api-key-here"
+api_version = "2024-12-01-preview"
+
+client = AzureOpenAI(
+    api_version=api_version,
+    azure_endpoint=endpoint,
+    api_key=subscription_key,
+)
+
+response = client.chat.completions.create(
+    messages=[
+        {
+            "role": "system",
+            "content": "You are a helpful assistant.",
+        },
+        {
+            "role": "user",
+            "content": "I am going to Roquefort-sur-Garonne, what should I see?",
+        }
+    ],
+    max_completion_tokens=100000,
+    model=deployment
+)
+
+print(response.choices[0].message.content)
\ No newline at end of file
diff --git a/sandbox/test_orange-tap_endpoint.sh b/sandbox/test_orange-tap_endpoint.sh
new file mode 100755
index 0000000..97881a1
--- /dev/null
+++ b/sandbox/test_orange-tap_endpoint.sh
@@ -0,0 +1,66 @@
+# Orange Tap endpoint info
+ORANGE_TAP_SERVICE_URL=http://bus-o4-15-model-0-svc-8009.swang.svc.cluster.local:8009
+ORANGE_TAP_ENDPOINT=https://***************:443
+
+# Orange Tap bus info
+ORANGE_TAP_X_BUS_USER=msft
+ORANGE_TAP_X_BUS_SNAPSHOT=az://orngcresco/models/snapshots/models.tc/neutrino-o1.5-mini-base-before-posttraining-transferred-********-decrypted
+ORANGE_TAP_X_BUS_RENDERER=harmony_v4.0.15_berry_v3_1mil_orion_lpe
+
+# Get access token
+echo "Getting access token..."
+access_token=$(az account get-access-token --resource 2a750fd4-529b-4678-b332-6331e201131c --query accessToken -o tsv)
+echo ""
+if [ -z "$access_token" ]; then
+  echo "access_token is empty"
+else
+  echo "access_token is not empty"
+fi
+echo ""
+
+# Display OrangeTap info
+echo ORANGE_TAP_ENDPOINT=$ORANGE_TAP_ENDPOINT
+echo ORANGE_TAP_SERVICE_URL=$ORANGE_TAP_SERVICE_URL
+echo ORANGE_TAP_X_BUS_SNAPSHOT=$ORANGE_TAP_X_BUS_SNAPSHOT
+echo ORANGE_TAP_X_BUS_USER=$ORANGE_TAP_X_BUS_USER
+echo ORANGE_TAP_X_BUS_RENDERER=$ORANGE_TAP_X_BUS_RENDERER
+echo ""
+
+# Send a sample curl request
+NODE_TLS_REJECT_UNAUTHORIZED=0
+echo "Sending CURL request..."
+echo ""
+curl -k "$ORANGE_TAP_ENDPOINT/v1/inference"   -H "Content-Type: application/json"   -H "Authorization: Bearer $access_token"   -H "SERVICE_URL: $ORANGE_TAP_SERVICE_URL"   -H "X-Bus-User: $ORANGE_TAP_X_BUS_USER"   -H "X-Bus-Snapshot: $ORANGE_TAP_X_BUS_SNAPSHOT"   -H "X-Bus-Renderer: $ORANGE_TAP_X_BUS_RENDERER"   -d '{
+    "temperature": 0,
+    "messages": [
+      {"role": "system", "content": "You are an AI assistant who answers user questions."},
+      {"role": "user", "content": "What should I visit in Skykomish, WA?"}
+    ],
+    "top_p": 0.95,
+    "frequency_penalty": 0,
+    "presence_penalty": 0,
+    "parallel_tool_calls": false,
+    "tools": [
+        {
+            "type": "function",
+            "function": {
+                "name": "bash",
+                "description": "Runs a bash command in an interactive bash",
+                "parameters": {
+                    "type": "object",
+                    "properties": {
+                        "command": {
+                            "type": "string",
+                            "description": "The bash command and arguments to run...."
+                        }
+                    },
+                    "required": [
+                        "command",
+                        "sessionId",
+                        "async"
+                    ]
+                }
+            }
+        }
+    ]
+  }'
diff --git a/tools/bench-agent/src/llm/azure-openai.ts b/tools/bench-agent/src/llm/azure-openai.ts
new file mode 100644
index 0000000..3cea57e
--- /dev/null
+++ b/tools/bench-agent/src/llm/azure-openai.ts
@@ -0,0 +1,133 @@
+import { ChatMessage } from '@web-bench/evaluator-types'
+import { Model as BaseModel, ScheduleTask } from '../type'
+import { LLMOption } from './base'
+import { OpenAILLM } from './openai'
+
+// Extended model interface for Azure OpenAI specific properties
+interface AzureModel extends BaseModel {
+  apiVersion?: string
+  deployment?: string
+}
+
+// Constants for better maintainability
+const AZURE_CONSTANTS = {
+  DEFAULT_CONTEXT_LENGTH: 10_000,
+  DEFAULT_MAX_TOKENS: 8192,
+  DEFAULT_TEMPERATURE: 0.4,
+  DEFAULT_API_VERSION: '2024-12-01-preview',
+  MAX_PARALLEL_TASKS: 10,
+  LOG_PREFIX: '[AzureOpenAI]',
+} as const
+
+export class AzureOpenAI extends OpenAILLM {
+  provider = 'azure-openai'
+  apiVersion: string // Match base class visibility
+  private azureInfo: AzureModel
+
+  option: LLMOption = {
+    contextLength: AZURE_CONSTANTS.DEFAULT_CONTEXT_LENGTH,
+    maxTokens: AZURE_CONSTANTS.DEFAULT_MAX_TOKENS,
+    temperature: AZURE_CONSTANTS.DEFAULT_TEMPERATURE,
+    apiBase: '', // Will be set from model config
+  }
+
+  constructor(info: AzureModel) {
+    super(info)
+    this.azureInfo = info
+    this.apiVersion = info.apiVersion || AZURE_CONSTANTS.DEFAULT_API_VERSION
+  }
+
+  protected _getEndpoint(endpoint: 'chat/completions' | 'completions' | 'models'): URL {
+    if (!this.apiBase) {
+      throw new Error(`${AZURE_CONSTANTS.LOG_PREFIX} No API base URL provided. Please set the 'apiBase' option in model.json`)
+    }
+
+    // Azure OpenAI endpoint format: 
+    // https://{resource-name}.openai.azure.com/openai/deployments/{deployment-name}/{endpoint}?api-version={api-version}
+    const deployment = this.azureInfo.deployment || this.azureInfo.model
+    const path = `openai/deployments/${deployment}/${endpoint}?api-version=${this.apiVersion}`
+    
+    try {
+      return new URL(path, this.apiBase)
+    } catch (error) {
+      throw new Error(`${AZURE_CONSTANTS.LOG_PREFIX} Invalid API base URL: ${this.apiBase}`)
+    }
+  }
+
+  protected _getHeaders() {
+    return {
+      'Content-Type': 'application/json',
+      Authorization: `Bearer ${this.info.apiKey}`, // Keep this for compatibility
+      'api-key': this.info.apiKey, // Azure uses api-key header
+    }
+  }
+
+  /**
+   * Checks if model is an o1 or o3 model that requires special handling
+   */
+  private isSpecialModel(model?: string): boolean {
+    if (!model) return false
+    return model.includes('o1-preview') || 
+           model.includes('o1-mini') || 
+           model.includes('o3')
+  }
+
+  /**
+   * Handles system message merging for o1/o3 models that don't support system messages
+   */
+  private handleSpecialModelMessages(messages: any[]): any[] {
+    const systemMessages = messages.filter(msg => msg?.role === 'system')
+    const nonSystemMessages = messages.filter(msg => msg?.role !== 'system')
+    
+    if (systemMessages.length > 0 && nonSystemMessages.length > 0) {
+      // Combine system message content with the first user message
+      const systemContent = systemMessages.map(msg => msg.content).join('\n\n')
+      const firstUserMessage = nonSystemMessages.find(msg => msg.role === 'user')
+      
+      if (firstUserMessage) {
+        firstUserMessage.content = `${systemContent}\n\n${firstUserMessage.content}`
+      }
+      
+      return nonSystemMessages
+    }
+    
+    // Fallback: remove all system messages if no user messages exist
+    return messages.filter(msg => msg?.role !== 'system')
+  }
+
+  protected _convertArgs(options: any, messages: ChatMessage[]) {
+    const baseOptions = {
+      messages: messages.map(this._convertMessage),
+      max_tokens: options.maxTokens,
+      temperature: options.temperature || this.option.temperature,
+      top_p: options.topP,
+      frequency_penalty: options.frequencyPenalty,
+      presence_penalty: options.presencePenalty,
+      stream: options.stream ?? true,
+      stop: options.stop,
+    }
+
+    // Handle o1 and o3 models which have special requirements
+    if (this.isSpecialModel(options.model)) {
+      return {
+        ...baseOptions,
+        max_completion_tokens: options.maxTokens,
+        max_tokens: undefined,
+        stream: false,
+        // o1 and o3 models don't support these parameters
+        temperature: undefined,
+        top_p: undefined,
+        frequency_penalty: undefined,
+        presence_penalty: undefined,
+        messages: this.handleSpecialModelMessages(baseOptions.messages),
+      }
+    }
+
+    return baseOptions
+  }
+
+  public checkLimit: (_: { runningTask: ScheduleTask[] }) => boolean = ({ runningTask }) => {
+    // Set the maximum number of parallel tasks for Azure to be conservative
+    return runningTask.length <= AZURE_CONSTANTS.MAX_PARALLEL_TASKS
+  }
+}
diff --git a/tools/bench-agent/src/llm/index.ts b/tools/bench-agent/src/llm/index.ts
index 846ac8e..5e1c0fe 100644
--- a/tools/bench-agent/src/llm/index.ts
+++ b/tools/bench-agent/src/llm/index.ts
@@ -1,53 +1,61 @@
-// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
-// 
-// Licensed under the Apache License, Version 2.0 (the "License");
-// you may not use this file except in compliance with the License.
-// You may obtain a copy of the License at
-// 
-//     http://www.apache.org/licenses/LICENSE-2.0
-// 
-// Unless required by applicable law or agreed to in writing, software
-// distributed under the License is distributed on an "AS IS" BASIS,
-// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
-// See the License for the specific language governing permissions and
-// limitations under the License.
-
-import { Model } from '../type'
-import { Aliyun } from './aliyun'
-import { AnthropicLLM } from './anthropic'
-import { BaseLLM } from './base'
-import { DeepseekLLM } from './deepseek'
-import { Doubao } from './doubao'
-import { Ollama } from './ollama'
-import { OpenAILLM } from './openai'
-import { OpenRouter } from './openrouter'
-
-export class LLMFactory {
-  static createLLM(info: Model): BaseLLM {
-    switch (info.provider) {
-      case 'anthropic': {
-        return new AnthropicLLM(info)
-      }
-      case 'openrouter': {
-        return new OpenRouter(info)
-      }
-      case 'openai': {
-        return new OpenAILLM(info)
-      }
-      case 'deepseek': {
-        return new DeepseekLLM(info)
-      }
-      case 'doubao': {
-        return new Doubao(info)
-      }
-      case 'aliyun': {
-        return new Aliyun(info)
-      }
-      case 'ollama': {
-        return new Ollama(info)
-      }
-      default:
-        throw Error(`Unknown provider: ${info.provider}`)
-    }
-  }
-}
+// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
+// 
+// Licensed under the Apache License, Version 2.0 (the "License");
+// you may not use this file except in compliance with the License.
+// You may obtain a copy of the License at
+// 
+//     http://www.apache.org/licenses/LICENSE-2.0
+// 
+// Unless required by applicable law or agreed to in writing, software
+// distributed under the License is distributed on an "AS IS" BASIS,
+// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
+// See the License for the specific language governing permissions and
+// limitations under the License.
+
+import { Model } from '../type'
+import { Aliyun } from './aliyun'
+import { AnthropicLLM } from './anthropic'
+import { AzureOpenAI } from './azure-openai'
+import { OrangeTap } from './orange-tap'
+import { BaseLLM } from './base'
+import { DeepseekLLM } from './deepseek'
+import { Doubao } from './doubao'
+import { Ollama } from './ollama'
+import { OpenAILLM } from './openai'
+import { OpenRouter } from './openrouter'
+
+export class LLMFactory {
+  static createLLM(info: Model): BaseLLM {
+    switch (info.provider) {
+      case 'anthropic': {
+        return new AnthropicLLM(info)
+      }
+      case 'openrouter': {
+        return new OpenRouter(info)
+      }
+      case 'openai': {
+        return new OpenAILLM(info)
+      }
+      case 'azure-openai': {
+        return new AzureOpenAI(info)
+      }
+      case 'orange-tap': {
+        return new OrangeTap(info)
+      }
+      case 'deepseek': {
+        return new DeepseekLLM(info)
+      }
+      case 'doubao': {
+        return new Doubao(info)
+      }
+      case 'aliyun': {
+        return new Aliyun(info)
+      }
+      case 'ollama': {
+        return new Ollama(info)
+      }
+      default:
+        throw Error(`Unknown provider: ${info.provider}`)
+    }
+  }
+}
diff --git a/tools/bench-agent/src/llm/orange-tap.ts b/tools/bench-agent/src/llm/orange-tap.ts
new file mode 100644
index 0000000..4ae12ca
--- /dev/null
+++ b/tools/bench-agent/src/llm/orange-tap.ts
@@ -0,0 +1,405 @@
+import { ChatMessage } from '@web-bench/evaluator-types'
+import { Model as BaseModel, ScheduleTask } from '../type'
+import { LLMOption } from './base'
+import { OpenAILLM } from './openai'
+import fetch from 'node-fetch'
+import https from 'node:https'
+
+// Extended model interface for Orange Tap specific properties
+interface OrangeTapModel extends BaseModel {
+  serviceUrl?: string
+  xBusUser?: string
+  xBusSnapshot?: string
+  xBusRenderer?: string
+  resourceId?: string
+  tokenRefreshIntervalMinutes?: string
+}
+
+// Cache for HTTPS agent to avoid recreating it on every request
+let httpsAgent: https.Agent | null = null
+
+// Token management
+interface TokenInfo {
+  token: string
+  expiresAt: number
+}
+
+let tokenCache: TokenInfo | null = null
+let tokenProvider: (() => Promise<{ token: string; expiresOnTimestamp: number }>) | null = null
+
+// Constants for better maintainability
+const ORANGE_TAP_CONSTANTS = {
+  DEFAULT_CONTEXT_LENGTH: 16_000,
+  DEFAULT_MAX_TOKENS: 8192,
+  DEFAULT_TEMPERATURE: 0.4,
+  ENDPOINT_PATH: '/v1/inference',
+  MAX_PARALLEL_TASKS: 10,
+  LOG_PREFIX: '[OrangeTap]',
+  RESPONSE_LOG_LIMIT: 2000,
+  TOOL_CONFIG: {
+    type: "function" as const,
+    function: {
+      name: "bash",
+      description: "Runs a bash command in an interactive bash",
+      parameters: {
+        type: "object",
+        properties: {
+          command: {
+            type: "string",
+            description: "The bash command and arguments to run...."
+          }
+        },
+        required: ["command", "sessionId", "async"]
+      }
+    }
+  }
+} as const
+export class OrangeTap extends OpenAILLM {
+  provider = 'orange-tap'
+  private orangeTapInfo: OrangeTapModel
+  private tokenRefreshIntervalMs: number
+
+  option: LLMOption = {
+    contextLength: ORANGE_TAP_CONSTANTS.DEFAULT_CONTEXT_LENGTH,
+    maxTokens: ORANGE_TAP_CONSTANTS.DEFAULT_MAX_TOKENS,
+    temperature: ORANGE_TAP_CONSTANTS.DEFAULT_TEMPERATURE,
+    apiBase: '', // Will be set from model config
+  }
+
+  constructor(info: OrangeTapModel) {
+    super(info)
+    this.orangeTapInfo = info
+    
+    // Parse token refresh interval from minutes to milliseconds
+    const intervalMinutes = parseInt(info.tokenRefreshIntervalMinutes || '5', 10)
+    this.tokenRefreshIntervalMs = intervalMinutes * 60 * 1000
+    
+    console.log(`${ORANGE_TAP_CONSTANTS.LOG_PREFIX} Token refresh interval set to ${intervalMinutes} minutes`)
+    
+    // Token provider will be initialized on first use
+  }
+
+  /**
+   * Initialize the Azure credential token provider using Azure CLI (only once globally)
+   */
+  private async initializeTokenProvider(): Promise<void> {
+    // Skip if already initialized globally
+    if (tokenProvider) {
+      console.log(`${ORANGE_TAP_CONSTANTS.LOG_PREFIX} Azure CLI token provider already initialized`)
+      return
+    }
+    
+    try {
+      console.log(`${ORANGE_TAP_CONSTANTS.LOG_PREFIX} Initializing Azure CLI token provider`)
+      
+      const { spawn } = await import('child_process')
+      
+      // Use a fixed resource ID globally (not dependent on instance)
+      const resourceId = '2a750fd4-529b-4678-b332-6331e201131c'
+      
+      // Create a simple token provider function using Azure CLI
+      tokenProvider = async () => {
+        return new Promise((resolve, reject) => {
+          const azProcess = spawn('az', [
+            'account', 
+            'get-access-token', 
+            '--resource', 
+            resourceId, 
+            '--query', 
+            'accessToken', 
+            '-o', 
+            'tsv'
+          ])
+          
+          let token = ''
+          let error = ''
+          
+          azProcess.stdout.on('data', (data) => {
+            token += data.toString()
+          })
+          
+          azProcess.stderr.on('data', (data) => {
+            error += data.toString()
+          })
+          
+          azProcess.on('close', (code) => {
+            if (code === 0) {
+              const cleanToken = token.trim()
+              if (cleanToken) {
+                resolve({
+                  token: cleanToken,
+                  expiresOnTimestamp: Date.now() + (60 * 60 * 1000) // 1 hour from now
+                })
+              } else {
+                reject(new Error('Empty token received from Azure CLI'))
+              }
+            } else {
+              reject(new Error(`Azure CLI failed with code ${code}: ${error}`))
+            }
+          })
+        })
+      }
+      
+      console.log(`${ORANGE_TAP_CONSTANTS.LOG_PREFIX} Azure CLI token provider initialized successfully`)
+    } catch (error) {
+      console.error(`${ORANGE_TAP_CONSTANTS.LOG_PREFIX} Failed to initialize token provider:`, error)
+      throw error
+    }
+  }
+
+  /**
+   * Get a valid token, refreshing if necessary
+   */
+  private async getValidToken(): Promise<string> {
+    const now = Date.now()
+    
+    // Debug logging
+    console.log(`${ORANGE_TAP_CONSTANTS.LOG_PREFIX} getValidToken called - tokenCache exists: ${!!tokenCache}, now: ${new Date(now).toISOString()}`)
+    
+    // Check if we have a valid cached token
+    if (tokenCache && tokenCache.expiresAt > now + 60000) { // 1 minute buffer
+      const remainingMs = tokenCache.expiresAt - now
+      const remainingMinutes = Math.round(remainingMs / 60000)
+      console.log(`${ORANGE_TAP_CONSTANTS.LOG_PREFIX} Using cached token (will refresh at ${new Date(tokenCache.expiresAt).toISOString()}, ${remainingMinutes} minutes remaining)`)
+      return tokenCache.token
+    }
+    
+    // Log why we're fetching a new token
+    if (!tokenCache) {
+      console.log(`${ORANGE_TAP_CONSTANTS.LOG_PREFIX} No cached token available`)
+    } else {
+      const timeUntilExpiry = tokenCache.expiresAt - now
+      console.log(`${ORANGE_TAP_CONSTANTS.LOG_PREFIX} Cached token expires in ${Math.round(timeUntilExpiry / 1000)}s, refreshing due to buffer`)
+    }
+    
+    // Initialize token provider if not done yet (only once globally)
+    if (!tokenProvider) {
+      await this.initializeTokenProvider()
+    }
+    
+    try {
+      console.log(`${ORANGE_TAP_CONSTANTS.LOG_PREFIX} Fetching new Azure AD token`)
+      
+      if (!tokenProvider) {
+        throw new Error('Token provider not initialized')
+      }
+      
+      const tokenResponse = await tokenProvider()
+      // Use the configured refresh interval from .env file
+      const expiresAt = now + this.tokenRefreshIntervalMs
+      
+      tokenCache = {
+        token: tokenResponse.token,
+        expiresAt: expiresAt
+      }
+      
+      console.log(`${ORANGE_TAP_CONSTANTS.LOG_PREFIX} Token obtained successfully, will refresh at ${new Date(expiresAt).toISOString()}`)
+      return tokenCache.token
+    } catch (error) {
+      console.error(`${ORANGE_TAP_CONSTANTS.LOG_PREFIX} Failed to obtain token:`, error)
+      throw error
+    }
+  }
+
+  protected _getEndpoint(endpoint: 'chat/completions' | 'completions' | 'models'): URL {
+    // Orange Tap uses a fixed endpoint, ignoring the endpoint parameter
+    return new URL(ORANGE_TAP_CONSTANTS.ENDPOINT_PATH, this.apiBase)
+  }
+
+  /**
+   * Creates or retrieves a cached HTTPS agent with TLS verification disabled
+   */
+  private getHttpsAgent(): https.Agent {
+    if (!httpsAgent) {
+      httpsAgent = new https.Agent({ rejectUnauthorized: false })
+    }
+    return httpsAgent
+  }
+
+  /**
+   * Normalizes URL by removing default HTTPS port if present
+   */
+  private normalizeUrl(url: URL): string {
+    if (url.protocol === 'https:' && url.port === '443') {
+      url.port = ''
+    }
+    return url.origin
+  }
+
+  /**
+   * Checks if the target URL matches the configured endpoint
+   */
+  private isTargetEndpoint(targetUrl: string): boolean {
+    try {
+      const endpointBase = new URL(this.orangeTapInfo.apiBase || this.option.apiBase)
+      const targetUrlObj = new URL(targetUrl)
+      
+      const endpointOrigin = this.normalizeUrl(endpointBase)
+      const targetOrigin = this.normalizeUrl(targetUrlObj)
+      
+      return targetOrigin === endpointOrigin
+    } catch (error) {
+      console.warn(`${ORANGE_TAP_CONSTANTS.LOG_PREFIX} Error checking endpoint match:`, error)
+      return false
+    }
+  }
+
+  /**
+   * Logs response details for debugging
+   */
+  private async logResponse(response: any): Promise<void> {
+    try {
+      console.log(`${ORANGE_TAP_CONSTANTS.LOG_PREFIX} Response status:`, response.status, response.statusText)
+      // Uncomment for detailed debugging:
+      // const text = await response.clone().text()
+      // console.log(`${ORANGE_TAP_CONSTANTS.LOG_PREFIX} Response headers:`, Object.fromEntries(response.headers.entries()))
+      // console.log(`${ORANGE_TAP_CONSTANTS.LOG_PREFIX} Response body:`, text.slice(0, ORANGE_TAP_CONSTANTS.RESPONSE_LOG_LIMIT))
+    } catch (error) {
+      console.warn(`${ORANGE_TAP_CONSTANTS.LOG_PREFIX} Failed to log response:`, error)
+    }
+  }
+
+  // Override fetch to disable cert check only for this endpoint AND inject authenticated headers
+  public async fetch(url: RequestInfo | URL, originInit?: RequestInit) {
+    const targetUrl = typeof url === 'string' ? url : url.toString()
+    const isTarget = this.isTargetEndpoint(targetUrl)
+    
+    // console.log(`${ORANGE_TAP_CONSTANTS.LOG_PREFIX} fetch called:`, { 
+    //   targetUrl, 
+    //   endpointPrefix: this.normalizeUrl(new URL(this.orangeTapInfo.apiBase || this.option.apiBase)), 
+    //   isTarget 
+    // })
+
+    if (isTarget) {
+      console.log(`${ORANGE_TAP_CONSTANTS.LOG_PREFIX} Using custom agent and authenticated headers for`, targetUrl)
+      
+      // Get authenticated headers and merge with any existing headers
+      const authenticatedHeaders = await this.getAuthenticatedHeaders()
+      const mergedHeaders = {
+        ...(originInit?.headers as Record<string, string> || {}),
+        ...authenticatedHeaders, // Our headers override any base class headers
+      }
+      
+      const safeInit: any = { 
+        ...originInit, 
+        agent: this.getHttpsAgent(),
+        headers: mergedHeaders
+      }
+      
+      // Remove null body to fix type issue
+      if (safeInit.body === null) {
+        delete safeInit.body
+      }
+      
+      return fetch(url as any, safeInit).then((res: any) => {
+        this.logResponse(res).catch(() => {}) // Fire and forget logging
+        return res
+      })
+    }
+    
+    // Otherwise, use default
+    return super.fetch(url, originInit)
+  }
+
+  protected _getHeaders() {
+    // This will be overridden by our custom fetch method
+    // Keep basic headers that don't require auth
+    return {
+      'Content-Type': 'application/json',
+      Authorization: `Bearer temp`, // Will be replaced
+      'api-key': 'temp', // Required by base class but not used in actual requests
+      'SERVICE_URL': this.orangeTapInfo.serviceUrl || '',
+      'X-Bus-User': this.orangeTapInfo.xBusUser || '',
+      'X-Bus-Snapshot': this.orangeTapInfo.xBusSnapshot || '',
+      'X-Bus-Renderer': this.orangeTapInfo.xBusRenderer || '',
+    }
+  }
+
+  /**
+   * Get headers with fresh token
+   */
+  private async getAuthenticatedHeaders(): Promise<Record<string, string>> {
+    const token = await this.getValidToken()
+    
+    return {
+      'Content-Type': 'application/json',
+      Authorization: `Bearer ${token}`,
+      'SERVICE_URL': this.orangeTapInfo.serviceUrl || '',
+      'X-Bus-User': this.orangeTapInfo.xBusUser || '',
+      'X-Bus-Snapshot': this.orangeTapInfo.xBusSnapshot || '',
+      'X-Bus-Renderer': this.orangeTapInfo.xBusRenderer || '',
+    }
+  }
+
+  /**
+   * Checks if model requires special o1/o3 handling
+   */
+  private isSpecialModel(model?: string): boolean {
+    if (!model) return false
+    return model.includes('o3') || model.includes('o1-preview') || model.includes('o1-mini')
+  }
+
+  /**
+   * Creates the default tool configuration for Orange Tap
+   */
+  private getDefaultTools() {
+    return [ORANGE_TAP_CONSTANTS.TOOL_CONFIG]
+  }
+
+  /**
+   * Handles system message merging for o1/o3 models
+   */
+  private handleSpecialModelMessages(messages: any[]): any[] {
+    const systemMessages = messages.filter(msg => msg?.role === 'system')
+    const nonSystemMessages = messages.filter(msg => msg?.role !== 'system')
+    
+    if (systemMessages.length > 0 && nonSystemMessages.length > 0) {
+      const systemContent = systemMessages.map(msg => msg.content).join('\n\n')
+      const firstUserMessage = nonSystemMessages.find(msg => msg.role === 'user')
+      
+      if (firstUserMessage) {
+        firstUserMessage.content = `${systemContent}\n\n${firstUserMessage.content}`
+      }
+      
+      return nonSystemMessages
+    }
+    
+    return messages.filter(msg => msg?.role !== 'system')
+  }
+
+  protected _convertArgs(options: any, messages: ChatMessage[]) {
+    const baseOptions = {
+      messages: messages.map(this._convertMessage),
+      temperature: 0,
+      top_p: 0.95,
+      frequency_penalty: 0,
+      presence_penalty: 0,
+      parallel_tool_calls: false,
+      tools: this.getDefaultTools(),
+      max_tokens: options.maxTokens,
+      stream: options.stream ?? true,
+      stop: options.stop,
+    }
+
+    // Handle special models (o1/o3) that have different requirements
+    if (this.isSpecialModel(options.model)) {
+      return {
+        ...baseOptions,
+        max_completion_tokens: options.maxTokens,
+        max_tokens: undefined,
+        stream: false,
+        temperature: undefined,
+        top_p: undefined,
+        frequency_penalty: undefined,
+        presence_penalty: undefined,
+        messages: this.handleSpecialModelMessages(baseOptions.messages),
+      }
+    }
+
+    return baseOptions
+  }
+
+  public checkLimit: (_: { runningTask: ScheduleTask[] }) => boolean = ({ runningTask }) => {
+    return runningTask.length <= ORANGE_TAP_CONSTANTS.MAX_PARALLEL_TASKS
+  }
+}
diff --git a/tools/bench-agent/src/prompt/index.ts b/tools/bench-agent/src/prompt/index.ts
index 1bafc55..6831ef5 100644
--- a/tools/bench-agent/src/prompt/index.ts
+++ b/tools/bench-agent/src/prompt/index.ts
@@ -11,8 +11,8 @@
 
 export const getSystemMessage = () => {
   const rules = [
-    `Always produce a single code block.`,
-    `Never separate the code into multiple code blocks.`,
+    `When creating multiple files, produce a separate code block for each file.`,
+    `Each code block must have the filename on the line immediately after the language specifier.`,
     `Only include the code that is being added.`,
     `No explanation, no issue, only code.`,
     `Never omit any code.`,
