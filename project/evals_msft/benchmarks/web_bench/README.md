# Web-Bench evaluation
This folder contains the resources to run the [Web-Bench](https://github.com/bytedance/web-bench) evaluation on models deployed to Azure OpenAI endpoints or to Orange buses accessed via Orange Tap.

**At this time, only _simple LLM's_ can be evaluated. They will use the default tools and orchestration provided with the benchmark. Evaluating _Agents_ (using their own tools and orchestration) is not yet supported.**

For any questions, reach out to thopo@ by email or on Teams.


## Requirements
- Access to Orange Tap or to an AOAI endpoint.
- Ubuntu 22.04 WSL with `git` and `conda`.
  - Only Ubuntu 22.04 has been tested so far. If you want to use a different OS or your Orange WSL, do so at your own risk... I definitely would NOT recommend using the Orange WSL, since the Web-Bench installation might mess it up.


## Set up
Run `source ~/code/glass/project/evals_msft/benchmarks/web_bench/init.sh`, which will do the following:
  - clone the web-bench repo (`4add558ab09c9b7bedbed21e5dcf6be1fa8fa23b` commit) and apply changes in a local `msft/web-bench` branch;
  - create and activate the `web_bench_env` conda environment;
  - install `build-essentials` if not already installed;
  - install `pnpm`, `rush`, and `playwright` if not already installed;
  - install the remaining dependencies with `rush update`;
  - build the project with `rush build`.


## Test connections
These are optional, but strongly recommended to ensure you are able to connect to an AOAI endpoint or to Orange Tap...

### AOAI connection

If you want to evaluate a model deployed to an AOAI endpoint, you should test the connexion as follows:
- adjust the `endpoint`, `model_name`, `deployment`, `subscription_key`, and `api_version` variables in `web-bench/sandbox/test_aoai_endpoint.py` to match your AOAI endpoint;
- run `python sandbox/test_aoai_endpoint.py`.

If you're getting an answer, you are good to go!

### Orange Tap connection

> For pre-requisites to be allowed to connect to an an Orange Tap endpoint, see [this Loop page](https://microsoft.sharepoint.com/:fl:/s/c7d610de-7039-4ddc-9274-577c06a9724d/EU02XoLfbfVDvC-WAsiURwUBEe8Im_vFvcMJZj7Ga1TagA?e=Vdt4cb&nav=cz0lMkZzaXRlcyUyRmM3ZDYxMGRlLTcwMzktNGRkYy05Mjc0LTU3N2MwNmE5NzI0ZCZkPWIlMjE5Z2lLNzBQUFdVeWRMMXVpa2JESkh2RDV4UEVNckI1Smctelc3S3ViV2dvMm1GS29IZlVNUTdMN1N3SURNSU55JmY9MDE1M1FFRkhTTkdaUElGWDNONlZCM1lMNFdBTEVKSVJZRiZjPSUyRiZhPUxvb3BBcHAmcD0lNDBmbHVpZHglMkZsb29wLXBhZ2UtY29udGFpbmVyJng9JTdCJTIydyUyMiUzQSUyMlQwUlRVSHh0YVdOeWIzTnZablF1YzJoaGNtVndiMmx1ZEM1amIyMThZaUU1WjJsTE56QlFVRmRWZVdSTU1YVnBhMkpFU2toMlJEVjRVRVZOY2tJMVNtY3RlbGMzUzNWaVYyZHZNbTFHUzI5SVpsVk5VVGRNTjFOM1NVUk5TVTU1ZkRBeE5UTlJSVVpJVkU5SVVrNDFVRXd5TWs1V1FVdEhORWMxVFUwMVdVeEZORU0lM0QlMjIlMkMlMjJpJTIyJTNBJTIyYmE5NzBjYjctZDU0My00YzRmLWJiYjEtODgwYjUzZDNlZGZkJTIyJTdE).

If you want to evaluate a model deployed to an Orange bus and accessed _via_ Orange Tap, you should test the connexion as follows:
- adjust the Orange Tap endpoint info and bus info in `web-bench/sandbox/test_orange-tap_endpoint.sh` to match your Orange Tap endpoint;
- do an `az login` with your regular @microsoft.com credentials (you don't have to provide an API key for this, the test script will get an access token based on your Azure identity);
- run `sandbox/test_orange-tap_endpoint.sh`.

If you're getting an answer, you are good to go!


## Run an evaluation

First, you will need to prepare the environment variables to point to your target endpoint (AOAI or Orange Tap), similar to what you did for testing the connection. Update the `web-bench/sandbox/env.env` file with your values, then rename it to `.env`, and copy it over to the `web-bench/apps/eval` folder. Note that for the Orange Tap endpoint, you don't have to provide an API key - the evaluation script will get an access token based on your Azure identity.

Second, you will need to update the `web-bench/sandbox/config.json5` file to choose which endpoint to evaluate (`azure-openai` or `orange-tap`) and on which project (comment out the `projects` entry to run on all projects). Then, copy this file over to `web-bench/apps/eval/src/config.json5`. **For the time being, you must keep the `agentMode` set to `local`.**

If you are in the Orange Tap scenario, refresh your credentials with `az login` (using your regular @microsoft.com account).

After that, you can run the evaluation with the following command:
```bash
nohup stdbuf -oL -eL rush eval 2>&1 | ts '[%Y-%m-%d %H:%M:%S]'      | tee -a run.log & disown
```

Results will be in `web-bench/apps/eval/report`, and logs will be available in both the standard output and the `web-bench/run.log` file.