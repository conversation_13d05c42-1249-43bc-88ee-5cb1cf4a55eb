#!/bin/bash
set -euo pipefail # fail fast, fail loud

# Change to the target directory
cd ~/code/glass/project/evals_msft/benchmarks/web_bench || exit 1

# Clone the web-bench repository if it doesn't already exist
if [ ! -d "web-bench" ]; then
  git clone https://github.com/bytedance/web-bench.git
fi

cd web-bench || exit 1

git fetch
# Checkout the specific commit
COMMIT_HASH="4add558ab09c9b7bedbed21e5dcf6be1fa8fa23b"
git checkout "$COMMIT_HASH"


# Create a new local branch only if it doesn't exist, and apply patch only if branch was just created
BRANCH_NAME="msft/web-bench"
if git show-ref --verify --quiet refs/heads/$BRANCH_NAME; then
  echo "Branch $BRANCH_NAME already exists. Skipping patch application."
  git checkout "$BRANCH_NAME"
else
  git checkout -b "$BRANCH_NAME"
  # Apply the patch
  if [ -f ../web-bench-changes.diff ]; then
    git apply ../web-bench-changes.diff
  else
    echo "Patch file ../web-bench-changes.diff not found!"
    exit 1
  fi
fi

# Conda environment setup
CONDA_ENV_NAME="web_bench_env"
ENV_YML_PATH="./sandbox/environment.yml"

if command -v conda >/dev/null 2>&1; then
  if conda info --envs | grep -q "^$CONDA_ENV_NAME[[:space:]]"; then
    echo "Conda environment $CONDA_ENV_NAME already exists."
  else
    if [ -f "$ENV_YML_PATH" ]; then
      echo "Creating conda environment $CONDA_ENV_NAME from $ENV_YML_PATH..."
      conda env create -f "$ENV_YML_PATH"
    else
      echo "Environment file $ENV_YML_PATH not found!"
      return 1 2>/dev/null || exit 1
    fi
  fi
  # shellcheck disable=SC1091
  source "$(conda info --base)/etc/profile.d/conda.sh"
  echo "Activating conda environment $CONDA_ENV_NAME..."
  # Activate the conda environment
  conda activate "$CONDA_ENV_NAME"
else
  echo "Conda is not installed or not in PATH. Please install Miniconda or Anaconda."
  return 1 2>/dev/null || exit 1
fi

# Ensure we are in the web-bench directory
cd ~/code/glass/project/evals_msft/benchmarks/web_bench/web-bench || return 1 2>/dev/null || exit 1

# Install build-essential if not already installed
echo "Installing build-essential if not already installed..."
if ! dpkg -s build-essential >/dev/null 2>&1; then
  sudo apt update
  sudo apt install -y build-essential
else
  echo "build-essential is already installed."
fi

# Install pnpm, rush, and playwright if not already installed
echo "Installing pnpm, rush, and playwright if not already installed..."

## Desired versions
PNPM_VER=9.12.0
RUSH_VER=5.140.0
PW_VER=1.49.1

## --- pnpm ----------------------------------------------------
if ! command -v pnpm >/dev/null 2>&1; then
  echo "→ Installing pnpm@$PNPM_VER..."
  npm install -g "pnpm@$PNPM_VER"
else
  echo "✓ pnpm already installed ($(pnpm -v))"
fi

## --- Rush ----------------------------------------------------
if ! command -v rush >/dev/null 2>&1; then
  echo "→ Installing @microsoft/rush@$RUSH_VER..."
  npm install -g "@microsoft/rush@$RUSH_VER"
else
  echo "✓ Rush already installed ($(rush --version | awk '{print $NF}'))"
fi

## --- Playwright CLI & browsers ------------------------------
if ! command -v playwright >/dev/null 2>&1; then
  echo "→ Installing playwright@$PW_VER..."
  npm install -g "playwright@$PW_VER"
else
  echo "✓ Playwright already installed ($(playwright --version | awk '{print $2}'))"
fi

## Install (or verify) browsers. Harmless to re-run.
echo "→ Ensuring Playwright browsers are installed..."
npx "playwright@$PW_VER" install

echo "All done with pnpm, rush, and playwright!"

# Install remaining dependencies
echo "Installing remaining dependencies with 'rush update'..."
rush update

# Build the project
echo "Building the project with 'rush build'..."
rush build

# Final message
echo "Setup complete ✅ . You are now in the web-bench directory, the conda environment is activated, all the Web-Bench dependencies should be available, and the project was built."
