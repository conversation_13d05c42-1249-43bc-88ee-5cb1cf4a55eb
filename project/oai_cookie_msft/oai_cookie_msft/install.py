#!/usr/bin/env python3
"""
Installation script for playwright and chromium browser.
This script installs the required dependencies for the oai_cookie module.
"""

import logging
import subprocess
import sys

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


def run_command(command, description):
    """
    Execute a command with proper error handling and progress indication.

    Args:
        command (list): Command to execute as a list of strings
        description (str): Description of what the command does

    Returns:
        bool: True if command succeeded, False otherwise
    """
    logger.info(f"Starting: {description}")
    print(f"🔄 {description}...")

    try:
        # Run the command and capture output
        result = subprocess.run(command, check=True, capture_output=True, text=True)

        logger.info(f"Command output: {result.stdout}")
        print(f"✅ {description} completed successfully")
        return True

    except subprocess.CalledProcessError as e:
        logger.error(f"Command failed with return code {e.returncode}")
        logger.error(f"Error output: {e.stderr}")
        print(f"❌ {description} failed!")
        print(f"Error: {e.stderr}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        print(f"❌ {description} failed with unexpected error: {str(e)}")
        return False


def install_chromium():
    """Install chromium browser for playwright."""
    return run_command(
        [sys.executable, "-m", "playwright", "install", "chromium"], "Installing chromium browser"
    )


def main():
    """Main installation process."""
    print("🚀 Starting playwright chromium installation...")
    print("=" * 50)

    # Install chromium browser
    if not install_chromium():
        print("\n❌ Installation failed at step 2: chromium browser installation")
        sys.exit(1)

    print("\n" + "=" * 50)
    print("🎉 All installations completed successfully!")
    print("You can now use playwright with chromium browser.")


if __name__ == "__main__":
    main()
